import taichi as ti
from taichi.math import *

ti.init(arch=ti.gpu, default_ip=ti.i32, default_fp=ti.f32)

image_resolution = (512, 512)
image_buffer = ti.Vector.field(4, float, image_resolution)
image_pixels = ti.Vector.field(3, float, image_resolution)

# --- Struct Definitions ---
Ray = ti.types.struct(origin=vec3, direction=vec3)
Material = ti.types.struct(albedo=vec3, emission=vec3)
Transform = ti.types.struct(position=vec3, rotation=vec3, scale=vec3)
SDFObject = ti.types.struct(distance=float, transform=Transform, material=Material)
HitRecord = ti.types.struct(object=SDFObject, position=vec3, distance=float, hit=bool)

# --- Scene Definition ---
objects = SDFObject.field(shape=8)
objects[0] = SDFObject(transform=Transform(vec3(0, 0, -1), vec3(0, 0, 0), vec3(1, 1, 0.2)),
                       material=Material(vec3(0.8, 0.8, 0.8), vec3(0)))      # Back wall
objects[1] = SDFObject(transform=Transform(vec3(0, 1, 0), vec3(90, 0, 0), vec3(1, 1, 0.2)),
                       material=Material(vec3(0.8, 0.8, 0.8), vec3(0)))      # Floor
objects[2] = SDFObject(transform=Transform(vec3(0, -1, 0), vec3(90, 0, 0), vec3(1, 1, 0.2)),
                       material=Material(vec3(0.8, 0.8, 0.8), vec3(0)))      # Ceiling
objects[3] = SDFObject(transform=Transform(vec3(-1, 0, 0), vec3(0, 90, 0), vec3(1, 1, 0.2)),
                       material=Material(vec3(0.8, 0.2, 0.2), vec3(0)))      # Left wall (Red)
objects[4] = SDFObject(transform=Transform(vec3(1, 0, 0), vec3(0, 90, 0), vec3(1, 1, 0.2)),
                       material=Material(vec3(0.2, 0.8, 0.2), vec3(0)))      # Right wall (Green)
objects[5] = SDFObject(transform=Transform(vec3(-0.275, -0.3, -0.2), vec3(0, 112, 0), vec3(0.25, 0.5, 0.25)),
                       material=Material(vec3(0.8, 0.8, 0.8), vec3(0)))      # Taller box
objects[6] = SDFObject(transform=Transform(vec3(0.275, -0.55, 0.2), vec3(0, -197, 0), vec3(0.25, 0.25, 0.25)),
                       material=Material(vec3(0.8, 0.8, 0.8), vec3(0)))      # Shorter box
objects[7] = SDFObject(transform=Transform(vec3(0, 0.809, 0), vec3(90, 0, 0), vec3(0.2, 0.2, 0.01)),
                       material=Material(vec3(0), vec3(20)))                # Light source

# --- SDF and Raymarching Functions ---

@ti.func
def angle(a: vec3) -> mat3:
    # [CORRECTED] The syntax mat3.rows() was wrong.
    # The correct way is to pass a list of row vectors to the constructor.
    s, c = sin(a), cos(a)
    m_z = mat3([c.z, s.z, 0], [-s.z, c.z, 0], [0, 0, 1])
    m_y = mat3([c.y, 0, -s.y], [0, 1, 0], [s.y, 0, c.y])
    m_x = mat3([1, 0, 0], [0, c.x, s.x], [0, -s.x, c.x])
    return m_z @ m_y @ m_x

@ti.func
def signed_distance(obj: SDFObject, pos: vec3) -> float:
    p = angle(radians(obj.transform.rotation)) @ (pos - obj.transform.position)
    q = abs(p) - obj.transform.scale
    return length(max(q, 0)) + min(max(q.x, max(q.y, q.z)), 0)

@ti.func
def nearest_object(p: vec3) -> SDFObject:
    o = objects[0]
    o.distance = abs(signed_distance(o, p))
    for i in ti.static(range(1, 8)):
        oi = objects[i]
        oi.distance = abs(signed_distance(oi, p))
        if oi.distance < o.distance:
            o = oi
    return o

@ti.func
def calc_normal(obj: SDFObject, p: vec3) -> vec3:
    e = vec2(1, -1) * 0.5773 * 0.005
    return normalize(
        e.xyy * signed_distance(obj, p + e.xyy) +
        e.yyx * signed_distance(obj, p + e.yyx) +
        e.yxy * signed_distance(obj, p + e.yxy) +
        e.xxx * signed_distance(obj, p + e.xxx)
    )

@ti.func
def raycast(ray: Ray) -> HitRecord:
    record = HitRecord(distance=0.0005)
    for _ in range(256):
        record.position = ray.origin + record.distance * ray.direction
        record.object = nearest_object(record.position)
        record.distance += record.object.distance
        record.hit = record.object.distance < 0.00001
        if record.distance > 2000.0 or record.hit:
            break
    return record

# --- Path Tracing and Sampling Functions ---

@ti.func
def create_onb(normal: vec3) -> mat3:
    b1 = vec3(0)
    if abs(normal.x) > abs(normal.z):
        b1 = normalize(vec3(-normal.y, normal.x, 0.0))
    else:
        b1 = normalize(vec3(0.0, -normal.z, normal.y))
    b2 = cross(normal, b1)
    return mat3(b1, b2, normal)

@ti.func
def cosine_weighted_hemisphere_sampling(normal: vec3) -> vec3:
    r1, r2 = ti.random(), ti.random()
    phi = 2.0 * pi * r1
    sqrt_r2 = sqrt(r2)
    
    local_dir = vec3(
        cos(phi) * sqrt_r2,
        sin(phi) * sqrt_r2,
        sqrt(1.0 - r2)
    )
    onb = create_onb(normal)
    return onb @ local_dir

@ti.func
def raytrace(ray: Ray) -> vec3:
    accumulated_color = vec3(0.0)
    throughput = vec3(1.0)
    
    max_bounces = 8
    for i in range(max_bounces):
        record = raycast(ray)

        if not record.hit:
            break
        
        emission = record.object.material.emission
        if any(emission > 0):
            accumulated_color += throughput * emission
            break
        
        p = max(throughput.x, throughput.y, throughput.z)
        if i > 2 and ti.random() > p:
            break
        
        if i > 2:
            throughput /= p

        albedo = record.object.material.albedo
        throughput *= albedo

        normal = calc_normal(record.object, record.position)
        
        ray.origin = record.position
        ray.direction = cosine_weighted_hemisphere_sampling(normal)

    return accumulated_color

# --- Main Render Kernel and UI Loop ---

@ti.kernel
def render(camera_position: vec3, camera_lookat: vec3, camera_up: vec3):
    for i, j in image_pixels:
        buffer = image_buffer[i, j]

        z = normalize(camera_position - camera_lookat)
        x = normalize(cross(camera_up, z))
        y = cross(z, x)
        
        half_width = half_height = tan(radians(35) * 0.5)
        lower_left_corner = camera_position - half_width * x - half_height * y - z
        horizontal = 2.0 * half_width * x
        vertical = 2.0 * half_height * y

        uv = (vec2(i, j) + vec2(ti.random(), ti.random())) / vec2(image_resolution)
        po = lower_left_corner + uv.x * horizontal + uv.y * vertical
        rd = normalize(po - camera_position)

        color = raytrace(Ray(origin=camera_position, direction=rd))
        buffer += vec4(color, 1.0)
        image_buffer[i, j] = buffer

        avg_color = buffer.rgb / buffer.a
        avg_color = pow(avg_color, vec3(1.0 / 2.2))
        color_mat = mat3(0.59719, 0.35458, 0.04823, 0.07600, 0.90834, 0.01566, 0.02840, 0.13383, 0.83777)
        avg_color = color_mat @ avg_color
        avg_color = (avg_color * (avg_color + 0.024578) - 0.0000905) / (avg_color * (0.983729 * avg_color + 0.4329510) + 0.238081)
        color_mat_inv = mat3(1.60475, -0.53108, -0.07367, -0.10208, 1.10813, -0.00605, -0.00327, -0.07276, 1.07602)
        avg_color = color_mat_inv @ avg_color
        
        image_pixels[i, j] = clamp(avg_color, 0, 1)

def main():
    window = ti.ui.Window("Cornell Box (Optimized & Fixed)", image_resolution)
    canvas = window.get_canvas()
    # To see the progressive rendering, clear the buffer once before the loop
    image_buffer.fill(0)
    while window.running:
        render(vec3(0, 0, 3.5), vec3(0, 0, -1), vec3(0, 1, 0))
        canvas.set_image(image_pixels)
        window.show()

if __name__ == '__main__':
    main()